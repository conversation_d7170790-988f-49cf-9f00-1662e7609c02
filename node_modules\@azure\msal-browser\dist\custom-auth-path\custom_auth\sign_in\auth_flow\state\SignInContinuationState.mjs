/*! @azure/msal-browser v4.15.0 2025-07-08 */
'use strict';
import { CustomAuthAccountData } from '../../../get_account/auth_flow/CustomAuthAccountData.mjs';
import { SignInResult } from '../result/SignInResult.mjs';
import { SignInState } from './SignInState.mjs';
import { SignInCompletedState } from './SignInCompletedState.mjs';

/*
 * Copyright (c) Microsoft Corporation. All rights reserved.
 * Licensed under the MIT License.
 */
/*
 * Sign-in continuation state.
 */
class SignInContinuationState extends SignInState {
    /**
     * Initiates the sign-in flow with continuation token.
     * @param {SignInWithContinuationTokenInputs} signInWithContinuationTokenInputs - The result of the operation.
     * @returns {Promise<SignInResult>} The result of the operation.
     */
    async signIn(signInWithContinuationTokenInputs) {
        try {
            const continuationTokenParams = {
                clientId: this.stateParameters.config.auth.clientId,
                correlationId: this.stateParameters.correlationId,
                challengeType: this.stateParameters.config.customAuth.challengeTypes ?? [],
                scopes: signInWithContinuationTokenInputs?.scopes ?? [],
                continuationToken: this.stateParameters.continuationToken ?? "",
                username: this.stateParameters.username,
                signInScenario: this.stateParameters.signInScenario,
            };
            this.stateParameters.logger.verbose("Signing in with continuation token.", this.stateParameters.correlationId);
            const completedResult = await this.stateParameters.signInClient.signInWithContinuationToken(continuationTokenParams);
            this.stateParameters.logger.verbose("Signed in with continuation token.", this.stateParameters.correlationId);
            const accountInfo = new CustomAuthAccountData(completedResult.authenticationResult.account, this.stateParameters.config, this.stateParameters.cacheClient, this.stateParameters.logger, this.stateParameters.correlationId);
            return new SignInResult(new SignInCompletedState(), accountInfo);
        }
        catch (error) {
            this.stateParameters.logger.errorPii(`Failed to sign in with continuation token. Error: ${error}.`, this.stateParameters.correlationId);
            return SignInResult.createWithError(error);
        }
    }
}

export { SignInContinuationState };
//# sourceMappingURL=SignInContinuationState.mjs.map
