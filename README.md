# Agent Zero - VS Code Extension

Agent Zero is a powerful AI assistant extension for Visual Studio Code that brings autonomous AI agent capabilities directly into your development environment. This extension transforms the original Agent Zero framework into a seamless VS Code experience.

## Features

### 🤖 Intelligent AI Assistant
- **Multi-Model Support**: Works with OpenAI, Anthropic, Google Gemini, Groq, Ollama, and many other AI providers
- **Context-Aware**: Understands your codebase and provides relevant assistance
- **Code Generation**: Generate code in any programming language
- **Code Analysis**: Analyze and improve existing code

### 🔧 Integrated Development Tools
- **File Operations**: Read, write, and modify files directly through the AI
- **Code Execution**: Run code snippets in various languages
- **Terminal Integration**: Execute terminal commands through the agent
- **Git Integration**: Work with version control through AI assistance

### 🧠 Memory & Knowledge Management
- **Persistent Memory**: Agents remember previous conversations and solutions
- **Knowledge Base**: Build and maintain a project-specific knowledge base
- **Learning**: Agents learn from your coding patterns and preferences

### 👥 Multi-Agent System
- **Agent Hierarchy**: Create specialized sub-agents for different tasks
- **Task Delegation**: Agents can create and manage subordinate agents
- **Collaborative Work**: Multiple agents can work together on complex tasks

### 🎨 Modern Interface
- **Native VS Code Integration**: Seamlessly integrated with VS Code's UI
- **Chat Interface**: Intuitive chat-based interaction
- **Activity Bar**: Dedicated Agent Zero activity bar with multiple views
- **Command Palette**: Quick access to all Agent Zero functions

## Installation

1. Open VS Code
2. Go to Extensions (Ctrl+Shift+X)
3. Search for "Agent Zero"
4. Click Install

## Quick Start

1. **Configure Your AI Models**:
   - Open VS Code Settings (Ctrl+,)
   - Search for "Agent Zero"
   - Configure your preferred AI providers and API keys

2. **Open Agent Zero**:
   - Click the Agent Zero icon in the Activity Bar
   - Or use Ctrl+Shift+A to open the chat

3. **Start Chatting**:
   - Type your request in the chat interface
   - Agent Zero will help you with coding, analysis, and development tasks

## Configuration

### AI Models

Configure your AI models in VS Code settings:

```json
{
  "agent-zero.models.chat.provider": "openai",
  "agent-zero.models.chat.name": "gpt-4",
  "agent-zero.models.utility.provider": "openai",
  "agent-zero.models.utility.name": "gpt-3.5-turbo",
  "agent-zero.models.embeddings.provider": "openai",
  "agent-zero.models.embeddings.name": "text-embedding-ada-002"
}
```

### Environment Variables

Set your API keys as environment variables:

```bash
export OPENAI_API_KEY="your-openai-key"
export ANTHROPIC_API_KEY="your-anthropic-key"
export GOOGLE_API_KEY="your-google-key"
```

## Commands

| Command | Shortcut | Description |
|---------|----------|-------------|
| `Agent Zero: Open Chat` | Ctrl+Shift+A | Open the main chat interface |
| `Agent Zero: Create New Agent` | Ctrl+Shift+N | Create a new specialized agent |
| `Agent Zero: Execute Code` | Ctrl+Shift+E | Execute selected code with Agent Zero |
| `Agent Zero: Analyze File` | - | Analyze the current file |
| `Agent Zero: Generate Code` | - | Generate code based on description |
| `Agent Zero: Settings` | - | Open Agent Zero settings |

## Views

### Chat View
The main interface for interacting with Agent Zero. Features:
- Real-time conversation with AI agents
- Code syntax highlighting
- Message history
- Export/import chat functionality

### Active Agents View
Manage your AI agents:
- View all active agents
- Create new agents
- Switch between agents
- Monitor agent status

### Memory & Knowledge View
Manage the AI's memory and knowledge:
- View stored memories
- Browse knowledge base
- Add custom knowledge
- Search through memories

### Tools View
Monitor and manage available tools:
- File operations
- Code execution
- Web browsing
- Custom tools

## Use Cases

### Code Development
```
"Create a React component for a user profile card with TypeScript"
"Refactor this function to use async/await"
"Add error handling to this API call"
```

### Code Analysis
```
"Analyze this file for potential security issues"
"Suggest performance improvements for this algorithm"
"Review this code for best practices"
```

### Project Management
```
"Create a project structure for a Node.js API"
"Generate unit tests for this module"
"Set up a CI/CD pipeline configuration"
```

### Learning & Documentation
```
"Explain how this algorithm works"
"Generate documentation for this function"
"Create examples for this API"
```

## Advanced Features

### Custom Agents
Create specialized agents for specific tasks:
- **Code Reviewer**: Focused on code quality and best practices
- **Test Writer**: Specialized in creating comprehensive tests
- **Documentation Writer**: Expert in creating clear documentation
- **Security Auditor**: Focused on security analysis

### Memory System
Agent Zero remembers:
- Previous solutions to similar problems
- Your coding preferences and patterns
- Project-specific context and requirements
- Custom instructions and guidelines

### Knowledge Base
Build a project-specific knowledge base:
- API documentation
- Coding standards
- Architecture decisions
- Common patterns and solutions

## Troubleshooting

### Common Issues

**Agent not responding**:
- Check your API key configuration
- Verify internet connection
- Check VS Code output panel for errors

**Code execution fails**:
- Ensure required runtime is installed
- Check file permissions
- Verify workspace settings

**Memory not persisting**:
- Check workspace folder permissions
- Verify VS Code storage access

### Support

- **GitHub Issues**: [Report bugs and feature requests](https://github.com/agent-zero-team/agent-zero-vscode/issues)
- **Documentation**: [Full documentation](https://github.com/agent-zero-team/agent-zero-vscode/wiki)
- **Community**: [Join our Discord](https://discord.gg/agent-zero)

## Contributing

We welcome contributions! Please see our [Contributing Guide](CONTRIBUTING.md) for details.

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## Acknowledgments

- Built on the original [Agent Zero](https://github.com/frdel/agent-zero) framework
- Powered by various AI providers and models
- Inspired by the VS Code extension ecosystem

---

**Made with ❤️ by the AmrDev Agent Zero Team**
