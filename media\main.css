/* Agent Zero Chat Styles */
body {
    font-family: var(--vscode-font-family);
    font-size: var(--vscode-font-size);
    color: var(--vscode-foreground);
    background-color: var(--vscode-editor-background);
    margin: 0;
    padding: 0;
    height: 100vh;
    overflow: hidden;
}

.chat-container {
    display: flex;
    flex-direction: column;
    height: 100vh;
    max-height: 100vh;
}

.chat-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 15px;
    border-bottom: 1px solid var(--vscode-panel-border);
    background-color: var(--vscode-editor-background);
    flex-shrink: 0;
}

.agent-info {
    display: flex;
    align-items: center;
    gap: 10px;
}

.agent-name {
    font-weight: bold;
    color: var(--vscode-textLink-foreground);
}

.agent-status {
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: bold;
    text-transform: uppercase;
}

.agent-status.idle {
    background-color: var(--vscode-charts-green);
    color: var(--vscode-editor-background);
}

.agent-status.working {
    background-color: var(--vscode-charts-yellow);
    color: var(--vscode-editor-background);
}

.agent-status.error {
    background-color: var(--vscode-charts-red);
    color: var(--vscode-editor-background);
}

.chat-controls {
    display: flex;
    gap: 5px;
}

.control-btn {
    background: none;
    border: 1px solid var(--vscode-button-border);
    color: var(--vscode-button-foreground);
    padding: 5px 8px;
    border-radius: 3px;
    cursor: pointer;
    font-size: 12px;
    transition: background-color 0.2s;
}

.control-btn:hover {
    background-color: var(--vscode-button-hoverBackground);
}

.messages-container {
    flex: 1;
    overflow-y: auto;
    padding: 15px;
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.welcome-message {
    text-align: center;
    padding: 20px;
    background-color: var(--vscode-editor-inactiveSelectionBackground);
    border-radius: 8px;
    border: 1px solid var(--vscode-panel-border);
}

.welcome-message h3 {
    margin: 0 0 10px 0;
    color: var(--vscode-textLink-foreground);
}

.welcome-message p {
    margin: 0;
    opacity: 0.8;
}

.message {
    display: flex;
    flex-direction: column;
    max-width: 85%;
    word-wrap: break-word;
    animation: fadeIn 0.3s ease-in;
}

.message.user {
    align-self: flex-end;
}

.message.agent {
    align-self: flex-start;
}

.message.system {
    align-self: center;
    max-width: 100%;
}

.message-content {
    padding: 10px 15px;
    border-radius: 12px;
    position: relative;
}

.message.user .message-content {
    background-color: var(--vscode-button-background);
    color: var(--vscode-button-foreground);
    border-bottom-right-radius: 4px;
}

.message.agent .message-content {
    background-color: var(--vscode-editor-inactiveSelectionBackground);
    border: 1px solid var(--vscode-panel-border);
    border-bottom-left-radius: 4px;
}

.message.system .message-content {
    background-color: var(--vscode-inputValidation-errorBackground);
    color: var(--vscode-inputValidation-errorForeground);
    border: 1px solid var(--vscode-inputValidation-errorBorder);
    text-align: center;
    font-style: italic;
}

.message-timestamp {
    font-size: 10px;
    opacity: 0.6;
    margin-top: 5px;
    text-align: right;
}

.message.agent .message-timestamp {
    text-align: left;
}

.typing-indicator {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 10px 15px;
    color: var(--vscode-descriptionForeground);
    font-style: italic;
    font-size: 12px;
}

.typing-dots {
    display: flex;
    gap: 3px;
}

.typing-dots span {
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background-color: var(--vscode-descriptionForeground);
    animation: typing 1.4s infinite ease-in-out;
}

.typing-dots span:nth-child(1) { animation-delay: -0.32s; }
.typing-dots span:nth-child(2) { animation-delay: -0.16s; }

.input-container {
    display: flex;
    padding: 15px;
    gap: 10px;
    border-top: 1px solid var(--vscode-panel-border);
    background-color: var(--vscode-editor-background);
    flex-shrink: 0;
}

#messageInput {
    flex: 1;
    min-height: 20px;
    max-height: 120px;
    padding: 10px 15px;
    border: 1px solid var(--vscode-input-border);
    border-radius: 20px;
    background-color: var(--vscode-input-background);
    color: var(--vscode-input-foreground);
    font-family: inherit;
    font-size: inherit;
    resize: none;
    outline: none;
    transition: border-color 0.2s;
}

#messageInput:focus {
    border-color: var(--vscode-focusBorder);
}

#messageInput::placeholder {
    color: var(--vscode-input-placeholderForeground);
}

.send-btn {
    width: 40px;
    height: 40px;
    border: none;
    border-radius: 50%;
    background-color: var(--vscode-button-background);
    color: var(--vscode-button-foreground);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background-color 0.2s, transform 0.1s;
    flex-shrink: 0;
}

.send-btn:hover {
    background-color: var(--vscode-button-hoverBackground);
}

.send-btn:active {
    transform: scale(0.95);
}

.send-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.status-bar {
    padding: 5px 15px;
    border-top: 1px solid var(--vscode-panel-border);
    background-color: var(--vscode-statusBar-background);
    color: var(--vscode-statusBar-foreground);
    font-size: 11px;
    text-align: right;
    flex-shrink: 0;
}

.char-count {
    opacity: 0.7;
}

/* Animations */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes typing {
    0%, 80%, 100% {
        transform: scale(0);
        opacity: 0.5;
    }
    40% {
        transform: scale(1);
        opacity: 1;
    }
}

/* Scrollbar styling */
.messages-container::-webkit-scrollbar {
    width: 8px;
}

.messages-container::-webkit-scrollbar-track {
    background: var(--vscode-scrollbarSlider-background);
}

.messages-container::-webkit-scrollbar-thumb {
    background: var(--vscode-scrollbarSlider-background);
    border-radius: 4px;
}

.messages-container::-webkit-scrollbar-thumb:hover {
    background: var(--vscode-scrollbarSlider-hoverBackground);
}

/* Code blocks in messages */
.message-content pre {
    background-color: var(--vscode-textCodeBlock-background);
    border: 1px solid var(--vscode-panel-border);
    border-radius: 4px;
    padding: 10px;
    margin: 10px 0;
    overflow-x: auto;
    font-family: var(--vscode-editor-font-family);
    font-size: var(--vscode-editor-font-size);
}

.message-content code {
    background-color: var(--vscode-textCodeBlock-background);
    padding: 2px 4px;
    border-radius: 3px;
    font-family: var(--vscode-editor-font-family);
}

/* Links in messages */
.message-content a {
    color: var(--vscode-textLink-foreground);
    text-decoration: none;
}

.message-content a:hover {
    text-decoration: underline;
}

/* Responsive design */
@media (max-width: 400px) {
    .chat-header {
        padding: 8px 10px;
    }
    
    .messages-container {
        padding: 10px;
    }
    
    .input-container {
        padding: 10px;
    }
    
    .message {
        max-width: 95%;
    }
}
