/*! @azure/msal-browser v4.15.0 2025-07-08 */
'use strict';
import { AuthFlowStateBase } from '../../../core/auth_flow/AuthFlowState.mjs';

/*
 * Copyright (c) Microsoft Corporation. All rights reserved.
 * Licensed under the MIT License.
 */
/**
 * Represents the completed state of the sign-in operation.
 * This state indicates that the sign-in process has finished successfully.
 */
class SignInCompletedState extends AuthFlowStateBase {
}

export { SignInCompletedState };
//# sourceMappingURL=SignInCompletedState.mjs.map
