/*! @azure/msal-browser v4.15.0 2025-07-08 */
'use strict';
import { ResetPasswordApiClient } from './ResetPasswordApiClient.mjs';
import { SignupApiClient } from './SignupApiClient.mjs';
import { SignInApiClient } from './SignInApiClient.mjs';

/*
 * Copyright (c) Microsoft Corporation. All rights reserved.
 * Licensed under the MIT License.
 */
class CustomAuthApiClient {
    constructor(customAuthApiBaseUrl, clientId, httpClient) {
        this.signInApi = new SignInApiClient(customAuthApiBaseUrl, clientId, httpClient);
        this.signUpApi = new SignupApiClient(customAuthApiBaseUrl, clientId, httpClient);
        this.resetPasswordApi = new ResetPasswordApiClient(customAuthApiBaseUrl, clientId, httpClient);
    }
}

export { CustomAuthApiClient };
//# sourceMappingURL=CustomAuthApiClient.mjs.map
