// Agent Zero Chat Interface
(function() {
    const vscode = acquireVsCodeApi();
    
    // DOM elements
    const messagesContainer = document.getElementById('messagesContainer');
    const messageInput = document.getElementById('messageInput');
    const sendBtn = document.getElementById('sendBtn');
    const clearBtn = document.getElementById('clearBtn');
    const exportBtn = document.getElementById('exportBtn');
    const importBtn = document.getElementById('importBtn');
    const typingIndicator = document.getElementById('typingIndicator');
    const agentName = document.getElementById('agentName');
    const agentStatus = document.getElementById('agentStatus');
    const charCount = document.getElementById('charCount');
    
    // State
    let messages = [];
    let isTyping = false;
    
    // Initialize
    init();
    
    function init() {
        setupEventListeners();
        updateCharCount();
        messageInput.focus();
    }
    
    function setupEventListeners() {
        // Send button click
        sendBtn.addEventListener('click', sendMessage);
        
        // Enter key to send (Shift+Enter for new line)
        messageInput.addEventListener('keydown', (e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                sendMessage();
            }
        });
        
        // Auto-resize textarea
        messageInput.addEventListener('input', () => {
            autoResizeTextarea();
            updateCharCount();
            updateSendButton();
        });
        
        // Control buttons
        clearBtn.addEventListener('click', clearChat);
        exportBtn.addEventListener('click', exportChat);
        importBtn.addEventListener('click', importChat);
        
        // Handle messages from extension
        window.addEventListener('message', handleExtensionMessage);
    }
    
    function autoResizeTextarea() {
        messageInput.style.height = 'auto';
        messageInput.style.height = Math.min(messageInput.scrollHeight, 120) + 'px';
    }
    
    function updateCharCount() {
        const count = messageInput.value.length;
        charCount.textContent = `${count}/4000`;
        
        if (count > 3800) {
            charCount.style.color = 'var(--vscode-inputValidation-warningForeground)';
        } else if (count >= 4000) {
            charCount.style.color = 'var(--vscode-inputValidation-errorForeground)';
        } else {
            charCount.style.color = 'var(--vscode-descriptionForeground)';
        }
    }
    
    function updateSendButton() {
        const hasText = messageInput.value.trim().length > 0;
        const notAtLimit = messageInput.value.length <= 4000;
        sendBtn.disabled = !hasText || !notAtLimit || isTyping;
    }
    
    function sendMessage() {
        const message = messageInput.value.trim();
        if (!message || message.length > 4000 || isTyping) {
            return;
        }
        
        // Clear input
        messageInput.value = '';
        autoResizeTextarea();
        updateCharCount();
        updateSendButton();
        
        // Send to extension
        vscode.postMessage({
            type: 'sendMessage',
            message: message
        });
    }
    
    function addMessage(messageData) {
        messages.push(messageData);
        
        const messageElement = createMessageElement(messageData);
        
        // Remove welcome message if it exists
        const welcomeMessage = messagesContainer.querySelector('.welcome-message');
        if (welcomeMessage) {
            welcomeMessage.remove();
        }
        
        messagesContainer.appendChild(messageElement);
        scrollToBottom();
    }
    
    function createMessageElement(messageData) {
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${messageData.sender}`;
        messageDiv.setAttribute('data-message-id', messageData.id);
        
        const contentDiv = document.createElement('div');
        contentDiv.className = 'message-content';
        
        // Process message content (handle markdown, code blocks, etc.)
        contentDiv.innerHTML = processMessageContent(messageData.content);
        
        const timestampDiv = document.createElement('div');
        timestampDiv.className = 'message-timestamp';
        timestampDiv.textContent = formatTimestamp(messageData.timestamp);
        
        messageDiv.appendChild(contentDiv);
        messageDiv.appendChild(timestampDiv);
        
        return messageDiv;
    }
    
    function processMessageContent(content) {
        // Basic processing - in a real implementation, you'd use a markdown parser
        let processed = content;
        
        // Escape HTML
        processed = processed.replace(/&/g, '&amp;')
                            .replace(/</g, '&lt;')
                            .replace(/>/g, '&gt;');
        
        // Handle code blocks
        processed = processed.replace(/```(\w+)?\n([\s\S]*?)```/g, (match, lang, code) => {
            return `<pre><code class="language-${lang || 'text'}">${code.trim()}</code></pre>`;
        });
        
        // Handle inline code
        processed = processed.replace(/`([^`]+)`/g, '<code>$1</code>');
        
        // Handle line breaks
        processed = processed.replace(/\n/g, '<br>');
        
        // Handle links (basic)
        processed = processed.replace(/(https?:\/\/[^\s]+)/g, '<a href="$1" target="_blank">$1</a>');
        
        return processed;
    }
    
    function formatTimestamp(timestamp) {
        const date = new Date(timestamp);
        return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    }
    
    function scrollToBottom() {
        messagesContainer.scrollTop = messagesContainer.scrollHeight;
    }
    
    function setTyping(typing) {
        isTyping = typing;
        typingIndicator.style.display = typing ? 'flex' : 'none';
        updateSendButton();
        
        if (typing) {
            scrollToBottom();
        }
    }
    
    function clearChat() {
        messages = [];
        messagesContainer.innerHTML = `
            <div class="welcome-message">
                <h3>Welcome to Agent Zero!</h3>
                <p>I'm your AI assistant. How can I help you today?</p>
            </div>
        `;
        
        vscode.postMessage({
            type: 'clearChat'
        });
    }
    
    function exportChat() {
        vscode.postMessage({
            type: 'exportChat'
        });
    }
    
    function importChat() {
        vscode.postMessage({
            type: 'importChat'
        });
    }
    
    function updateAgentInfo(agent) {
        agentName.textContent = agent.name;
        agentStatus.textContent = agent.status;
        agentStatus.className = `agent-status ${agent.status}`;
    }
    
    function handleExtensionMessage(event) {
        const message = event.data;
        
        switch (message.type) {
            case 'addMessage':
                addMessage(message.message);
                break;
                
            case 'setTyping':
                setTyping(message.isTyping);
                break;
                
            case 'clearMessages':
                clearChat();
                break;
                
            case 'agentChanged':
                updateAgentInfo(message.agent);
                break;
                
            case 'loadMessages':
                // Load previous messages
                messages = message.messages || [];
                renderMessages();
                break;
        }
    }
    
    function renderMessages() {
        // Clear container except welcome message
        const welcomeMessage = messagesContainer.querySelector('.welcome-message');
        messagesContainer.innerHTML = '';
        
        if (messages.length === 0 && welcomeMessage) {
            messagesContainer.appendChild(welcomeMessage);
        } else {
            messages.forEach(messageData => {
                const messageElement = createMessageElement(messageData);
                messagesContainer.appendChild(messageElement);
            });
            scrollToBottom();
        }
    }
    
    // Add some keyboard shortcuts
    document.addEventListener('keydown', (e) => {
        // Ctrl/Cmd + K to clear chat
        if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
            e.preventDefault();
            clearChat();
        }
        
        // Escape to focus input
        if (e.key === 'Escape') {
            messageInput.focus();
        }
    });
    
})();
