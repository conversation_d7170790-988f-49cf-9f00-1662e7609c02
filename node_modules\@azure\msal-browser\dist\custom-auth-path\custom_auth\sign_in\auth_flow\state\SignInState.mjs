/*! @azure/msal-browser v4.15.0 2025-07-08 */
'use strict';
import { AuthFlowActionRequiredStateBase } from '../../../core/auth_flow/AuthFlowState.mjs';
import { ensureArgumentIsNotEmptyString } from '../../../core/utils/ArgumentValidator.mjs';

/*
 * Copyright (c) Microsoft Corporation. All rights reserved.
 * Licensed under the MIT License.
 */
/*
 * Base state handler for sign-in flow.
 */
class SignInState extends AuthFlowActionRequiredStateBase {
    /*
     * Creates a new SignInState.
     * @param stateParameters - The state parameters for sign-in.
     */
    constructor(stateParameters) {
        super(stateParameters);
        ensureArgumentIsNotEmptyString("username", stateParameters.username, stateParameters.correlationId);
        ensureArgumentIsNotEmptyString("continuationToken", stateParameters.continuationToken, stateParameters.correlationId);
    }
}

export { SignInState };
//# sourceMappingURL=SignInState.mjs.map
