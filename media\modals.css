/* Modals CSS for Agent Zero VS Code Extension */

/* Base Modal Styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 10000;
  backdrop-filter: blur(2px);
}

.modal-container {
  background-color: var(--vscode-editor-background);
  border: 1px solid var(--vscode-panel-border);
  border-radius: 8px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
  width: 90%;
  max-width: 900px;
  max-height: 90%;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  animation: modalFadeIn 0.3s ease-out;
}

@keyframes modalFadeIn {
  from {
    opacity: 0;
    transform: scale(0.9) translateY(-20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-md) var(--spacing-lg);
  border-bottom: 1px solid var(--vscode-panel-border);
  background-color: var(--vscode-sideBar-background);
}

.modal-header h2 {
  margin: 0;
  color: var(--vscode-foreground);
  font-size: var(--font-size-large);
}

.modal-close {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: var(--vscode-foreground);
  padding: var(--spacing-xs);
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.modal-close:hover {
  background-color: var(--vscode-button-hoverBackground);
}

.modal-content {
  flex: 1;
  overflow-y: auto;
  padding: var(--spacing-lg);
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: var(--spacing-sm);
  padding: var(--spacing-md) var(--spacing-lg);
  border-top: 1px solid var(--vscode-panel-border);
  background-color: var(--vscode-sideBar-background);
}

/* Modal Buttons */
.modal-btn {
  padding: var(--spacing-xs) var(--spacing-md);
  border: 1px solid var(--vscode-button-border);
  border-radius: 4px;
  cursor: pointer;
  font-size: var(--font-size-small);
  transition: all 0.2s;
  min-width: 80px;
}

.modal-btn-primary {
  background-color: var(--vscode-button-background);
  color: var(--vscode-button-foreground);
}

.modal-btn-primary:hover {
  background-color: var(--vscode-button-hoverBackground);
}

.modal-btn-secondary {
  background-color: var(--vscode-button-secondaryBackground);
  color: var(--vscode-button-secondaryForeground);
}

.modal-btn-secondary:hover {
  background-color: var(--vscode-button-secondaryHoverBackground);
}

/* File Browser Modal */
.file-browser-modal .modal-container {
  max-width: 1000px;
  height: 80%;
}

.file-browser-content {
  display: flex;
  height: 100%;
}

.file-browser-sidebar {
  width: 200px;
  border-right: 1px solid var(--vscode-panel-border);
  padding: var(--spacing-md);
  background-color: var(--vscode-sideBar-background);
}

.file-browser-main {
  flex: 1;
  padding: var(--spacing-md);
  overflow-y: auto;
}

.file-browser-toolbar {
  display: flex;
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-md);
  padding-bottom: var(--spacing-sm);
  border-bottom: 1px solid var(--vscode-panel-border);
}

.file-browser-toolbar button {
  padding: var(--spacing-xs) var(--spacing-sm);
  background-color: var(--vscode-button-background);
  color: var(--vscode-button-foreground);
  border: 1px solid var(--vscode-button-border);
  border-radius: 4px;
  cursor: pointer;
  font-size: var(--font-size-small);
}

.file-browser-toolbar button:hover {
  background-color: var(--vscode-button-hoverBackground);
}

/* History Modal */
.history-modal .modal-container {
  max-width: 800px;
  height: 70%;
}

.history-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.history-item {
  padding: var(--spacing-sm) var(--spacing-md);
  border-bottom: 1px solid var(--vscode-panel-border);
  cursor: pointer;
  transition: background-color 0.2s;
}

.history-item:hover {
  background-color: var(--vscode-list-hoverBackground);
}

.history-item:last-child {
  border-bottom: none;
}

.history-timestamp {
  font-size: var(--font-size-small);
  color: var(--vscode-descriptionForeground);
  margin-bottom: var(--spacing-xxs);
}

.history-preview {
  color: var(--vscode-foreground);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* Context Window Modal */
.context-modal .modal-container {
  max-width: 900px;
  height: 80%;
}

.context-content {
  font-family: var(--vscode-editor-font-family);
  font-size: var(--vscode-editor-font-size);
  background-color: var(--vscode-textCodeBlock-background);
  border: 1px solid var(--vscode-panel-border);
  border-radius: 4px;
  padding: var(--spacing-md);
  height: 100%;
  overflow-y: auto;
  white-space: pre-wrap;
}

/* Confirmation Modal */
.confirmation-modal .modal-container {
  max-width: 400px;
}

.confirmation-content {
  text-align: center;
  padding: var(--spacing-lg);
}

.confirmation-icon {
  font-size: 48px;
  margin-bottom: var(--spacing-md);
}

.confirmation-icon.warning {
  color: var(--vscode-charts-yellow);
}

.confirmation-icon.error {
  color: var(--vscode-charts-red);
}

.confirmation-icon.info {
  color: var(--vscode-charts-blue);
}

.confirmation-message {
  margin-bottom: var(--spacing-lg);
  color: var(--vscode-foreground);
}

/* Loading Modal */
.loading-modal .modal-container {
  max-width: 300px;
  text-align: center;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid var(--vscode-panel-border);
  border-top: 4px solid var(--vscode-textLink-foreground);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto var(--spacing-md) auto;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-message {
  color: var(--vscode-foreground);
  margin-bottom: var(--spacing-md);
}

/* Responsive Design */
@media (max-width: 768px) {
  .modal-container {
    width: 95%;
    max-height: 95%;
  }
  
  .file-browser-content {
    flex-direction: column;
  }
  
  .file-browser-sidebar {
    width: 100%;
    border-right: none;
    border-bottom: 1px solid var(--vscode-panel-border);
  }
}
