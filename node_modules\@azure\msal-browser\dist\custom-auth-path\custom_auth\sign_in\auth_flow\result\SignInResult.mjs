/*! @azure/msal-browser v4.15.0 2025-07-08 */
'use strict';
import { AuthFlowResultBase } from '../../../core/auth_flow/AuthFlowResultBase.mjs';
import { SignInError } from '../error_type/SignInError.mjs';
import { SignInCodeRequiredState } from '../state/SignInCodeRequiredState.mjs';
import { SignInPasswordRequiredState } from '../state/SignInPasswordRequiredState.mjs';
import { SignInFailedState } from '../state/SignInFailedState.mjs';
import { SignInCompletedState } from '../state/SignInCompletedState.mjs';

/*
 * Copyright (c) Microsoft Corporation. All rights reserved.
 * Licensed under the MIT License.
 */
/*
 * Result of a sign-in operation.
 */
class SignInResult extends AuthFlowResultBase {
    /**
     * Creates a new instance of SignInResultState.
     * @param state The state of the result.
     */
    constructor(state, resultData) {
        super(state, resultData);
    }
    /**
     * Creates a new instance of SignInResult with an error.
     * @param error The error that occurred.
     * @returns {SignInResult} A new instance of SignInResult with the error set.
     */
    static createWithError(error) {
        const result = new SignInResult(new SignInFailedState());
        result.error = new SignInError(SignInResult.createErrorData(error));
        return result;
    }
    /**
     * Checks if the result is in a failed state.
     */
    isFailed() {
        return this.state instanceof SignInFailedState;
    }
    /**
     * Checks if the result is in a code required state.
     */
    isCodeRequired() {
        return this.state instanceof SignInCodeRequiredState;
    }
    /**
     * Checks if the result is in a password required state.
     */
    isPasswordRequired() {
        return this.state instanceof SignInPasswordRequiredState;
    }
    /**
     * Checks if the result is in a completed state.
     */
    isCompleted() {
        return this.state instanceof SignInCompletedState;
    }
}

export { SignInResult };
//# sourceMappingURL=SignInResult.mjs.map
