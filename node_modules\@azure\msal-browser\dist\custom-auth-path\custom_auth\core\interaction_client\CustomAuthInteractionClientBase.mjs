/*! @azure/msal-browser v4.15.0 2025-07-08 */
'use strict';
import { MethodNotImplementedError } from '../error/MethodNotImplementedError.mjs';
import { ChallengeType } from '../../CustomAuthConstants.mjs';
import { StandardInteractionClient } from '../../../interaction_client/StandardInteractionClient.mjs';
import { Constants } from '@azure/msal-common/browser';

/*
 * Copyright (c) Microsoft Corporation. All rights reserved.
 * Licensed under the MIT License.
 */
class CustomAuthInteractionClientBase extends StandardInteractionClient {
    constructor(config, storageImpl, browserCrypto, logger, eventHandler, navigationClient, performanceClient, customAuthApiClient, customAuthAuthority) {
        super(config, storageImpl, browserCrypto, logger, eventHandler, navigationClient, performanceClient);
        this.customAuthApiClient = customAuthApiClient;
        this.customAuthAuthority = customAuthAuthority;
    }
    getChallengeTypes(configuredChallengeTypes) {
        const challengeType = configuredChallengeTypes ?? [];
        if (!challengeType.some((type) => type.toLowerCase() === ChallengeType.REDIRECT)) {
            challengeType.push(ChallengeType.REDIRECT);
        }
        return challengeType.join(" ");
    }
    getScopes(scopes) {
        return [
            Constants.OPENID_SCOPE,
            Constants.PROFILE_SCOPE,
            Constants.OFFLINE_ACCESS_SCOPE,
        ];
    }
    // It is not necessary to implement this method from base class.
    acquireToken(
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    request) {
        throw new MethodNotImplementedError("SignInClient.acquireToken");
    }
    // It is not necessary to implement this method from base class.
    logout(
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    request) {
        throw new MethodNotImplementedError("SignInClient.logout");
    }
}

export { CustomAuthInteractionClientBase };
//# sourceMappingURL=CustomAuthInteractionClientBase.mjs.map
