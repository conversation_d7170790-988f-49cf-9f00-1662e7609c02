/* File Browser CSS for Agent Zero VS Code Extension */

/* Input Section Styles */
#input-section {
  border-top: 1px solid var(--vscode-panel-border);
  background-color: var(--vscode-editor-background);
  flex-shrink: 0;
}

/* Preview Section */
.preview-section {
  padding: var(--spacing-sm) var(--spacing-md);
  border-bottom: 1px solid var(--vscode-panel-border);
  background-color: var(--vscode-editor-inactiveSelectionBackground);
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-sm);
}

.preview-item {
  position: relative;
  display: flex;
  align-items: center;
  padding: var(--spacing-xs) var(--spacing-sm);
  background-color: var(--vscode-button-secondaryBackground);
  border: 1px solid var(--vscode-button-border);
  border-radius: 6px;
  max-width: 200px;
}

.preview-item.image-preview {
  flex-direction: column;
  padding: var(--spacing-xs);
}

.preview-item img {
  max-width: 100px;
  max-height: 100px;
  border-radius: 4px;
  object-fit: cover;
}

.file-preview {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.filename {
  font-size: var(--font-size-small);
  color: var(--vscode-foreground);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 120px;
}

.extension {
  font-size: var(--font-size-small);
  color: var(--vscode-descriptionForeground);
  font-weight: bold;
  text-transform: uppercase;
}

.remove-attachment {
  position: absolute;
  top: -5px;
  right: -5px;
  width: 18px;
  height: 18px;
  border-radius: 50%;
  background-color: var(--vscode-charts-red);
  color: white;
  border: none;
  cursor: pointer;
  font-size: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
}

.remove-attachment:hover {
  background-color: var(--vscode-errorForeground);
}

/* Input Row */
.input-row {
  display: flex;
  padding: var(--spacing-md);
  gap: var(--spacing-sm);
  align-items: flex-end;
}

/* Attachment Wrapper */
.attachment-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.attachment-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background-color: var(--vscode-button-secondaryBackground);
  border: 1px solid var(--vscode-button-border);
  border-radius: 8px;
  cursor: pointer;
  color: var(--vscode-button-secondaryForeground);
  transition: all 0.2s;
}

.attachment-icon:hover {
  background-color: var(--vscode-button-secondaryHoverBackground);
}

.attachment-icon svg {
  width: 20px;
  height: 20px;
}

.tooltip {
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  background-color: var(--vscode-editorHoverWidget-background);
  color: var(--vscode-editorHoverWidget-foreground);
  border: 1px solid var(--vscode-editorHoverWidget-border);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: 4px;
  font-size: var(--font-size-small);
  white-space: nowrap;
  z-index: 1000;
  margin-bottom: var(--spacing-xs);
}

.tooltip::after {
  content: '';
  position: absolute;
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  border: 5px solid transparent;
  border-top-color: var(--vscode-editorHoverWidget-border);
}

/* Chat Input Container */
#chat-input-container {
  position: relative;
  flex: 1;
  display: flex;
  align-items: flex-end;
}

#chat-input {
  flex: 1;
  min-height: 40px;
  max-height: 120px;
  padding: var(--spacing-sm) 40px var(--spacing-sm) var(--spacing-md);
  border: 1px solid var(--vscode-input-border);
  border-radius: 20px;
  background-color: var(--vscode-input-background);
  color: var(--vscode-input-foreground);
  font-family: var(--vscode-font-family);
  font-size: var(--vscode-font-size);
  resize: none;
  outline: none;
  transition: border-color 0.2s;
  line-height: 1.4;
}

#chat-input:focus {
  border-color: var(--vscode-focusBorder);
}

#chat-input::placeholder {
  color: var(--vscode-input-placeholderForeground);
}

#expand-button {
  position: absolute;
  right: var(--spacing-xs);
  bottom: var(--spacing-xs);
  width: 28px;
  height: 28px;
  background: none;
  border: none;
  color: var(--vscode-descriptionForeground);
  cursor: pointer;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s;
}

#expand-button:hover {
  background-color: var(--vscode-button-hoverBackground);
  color: var(--vscode-foreground);
}

#expand-button svg {
  width: 16px;
  height: 16px;
}

/* Chat Buttons Wrapper */
#chat-buttons-wrapper {
  display: flex;
  gap: var(--spacing-xs);
  align-items: center;
}

.chat-button {
  width: 40px;
  height: 40px;
  border: none;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s;
  flex-shrink: 0;
}

#send-button {
  background-color: var(--vscode-button-background);
  color: var(--vscode-button-foreground);
}

#send-button:hover {
  background-color: var(--vscode-button-hoverBackground);
  transform: scale(1.05);
}

#send-button:active {
  transform: scale(0.95);
}

#send-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

#microphone-button {
  background-color: var(--vscode-button-secondaryBackground);
  color: var(--vscode-button-secondaryForeground);
}

#microphone-button:hover {
  background-color: var(--vscode-button-secondaryHoverBackground);
}

#microphone-button.mic-active {
  background-color: var(--vscode-charts-red);
  color: white;
  animation: pulse 1.5s infinite;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(255, 0, 0, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(255, 0, 0, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(255, 0, 0, 0);
  }
}

/* Text Buttons Row */
.text-buttons-row {
  display: flex;
  gap: var(--spacing-xs);
  padding: 0 var(--spacing-md) var(--spacing-md);
  flex-wrap: wrap;
  justify-content: center;
}

.text-button {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  padding: var(--spacing-xs) var(--spacing-sm);
  background-color: var(--vscode-button-secondaryBackground);
  color: var(--vscode-button-secondaryForeground);
  border: 1px solid var(--vscode-button-border);
  border-radius: 6px;
  cursor: pointer;
  font-size: var(--font-size-small);
  transition: all 0.2s;
  text-decoration: none;
}

.text-button:hover {
  background-color: var(--vscode-button-secondaryHoverBackground);
  transform: translateY(-1px);
}

.text-button svg {
  width: 14px;
  height: 14px;
  flex-shrink: 0;
}

.text-button p {
  margin: 0;
  font-size: var(--font-size-small);
}

/* File Browser Specific Styles */
.file-item {
  display: flex;
  align-items: center;
  padding: var(--spacing-xs) var(--spacing-sm);
  border-bottom: 1px solid var(--vscode-panel-border);
  cursor: pointer;
  transition: background-color 0.2s;
}

.file-item:hover {
  background-color: var(--vscode-list-hoverBackground);
}

.file-item:last-child {
  border-bottom: none;
}

.file-icon {
  width: 20px;
  height: 20px;
  margin-right: var(--spacing-sm);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
}

.file-name {
  flex: 1;
  font-size: var(--font-size-small);
  color: var(--vscode-foreground);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.file-size {
  font-size: var(--font-size-small);
  color: var(--vscode-descriptionForeground);
  min-width: 60px;
  text-align: right;
}

.file-browser-toolbar {
  display: flex;
  gap: var(--spacing-xs);
  align-items: center;
  margin-bottom: var(--spacing-md);
  padding-bottom: var(--spacing-sm);
  border-bottom: 1px solid var(--vscode-panel-border);
}

.file-browser-toolbar button {
  padding: var(--spacing-xs) var(--spacing-sm);
  background-color: var(--vscode-button-background);
  color: var(--vscode-button-foreground);
  border: 1px solid var(--vscode-button-border);
  border-radius: 4px;
  cursor: pointer;
  font-size: var(--font-size-small);
  transition: background-color 0.2s;
}

.file-browser-toolbar button:hover {
  background-color: var(--vscode-button-hoverBackground);
}

.file-browser-toolbar button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

#file-browser-path {
  flex: 1;
  font-family: var(--vscode-editor-font-family);
  font-size: var(--font-size-small);
  color: var(--vscode-descriptionForeground);
  background-color: var(--vscode-input-background);
  border: 1px solid var(--vscode-input-border);
  border-radius: 4px;
  padding: var(--spacing-xs) var(--spacing-sm);
  margin-left: var(--spacing-sm);
}

.file-browser-shortcuts {
  list-style: none;
  padding: 0;
  margin: 0;
}

.file-browser-shortcuts li {
  margin-bottom: var(--spacing-xs);
}

.file-browser-shortcuts a {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  padding: var(--spacing-xs) var(--spacing-sm);
  text-decoration: none;
  color: var(--vscode-foreground);
  border-radius: 4px;
  transition: background-color 0.2s;
  font-size: var(--font-size-small);
}

.file-browser-shortcuts a:hover {
  background-color: var(--vscode-list-hoverBackground);
}

.file-browser-shortcuts .shortcut-icon {
  width: 16px;
  height: 16px;
  font-size: 14px;
}

#file-browser-list {
  flex: 1;
  overflow-y: auto;
  border: 1px solid var(--vscode-panel-border);
  border-radius: 4px;
  background-color: var(--vscode-editor-background);
}

.file-browser-empty {
  text-align: center;
  padding: var(--spacing-lg);
  color: var(--vscode-descriptionForeground);
  font-style: italic;
}

/* Responsive Design */
@media (max-width: 768px) {
  .input-row {
    flex-direction: column;
    gap: var(--spacing-sm);
  }

  #chat-input-container {
    order: 1;
  }

  .attachment-wrapper {
    order: 2;
    align-self: flex-start;
  }

  #chat-buttons-wrapper {
    order: 3;
    justify-content: center;
  }

  .text-buttons-row {
    flex-direction: column;
    align-items: stretch;
  }

  .text-button {
    justify-content: center;
  }

  .preview-section {
    flex-direction: column;
  }

  .preview-item {
    max-width: none;
  }

  .file-browser-content {
    flex-direction: column;
  }

  .file-browser-sidebar {
    width: 100%;
    border-right: none;
    border-bottom: 1px solid var(--vscode-panel-border);
  }

  .file-browser-toolbar {
    flex-wrap: wrap;
  }

  #file-browser-path {
    width: 100%;
    margin-left: 0;
    margin-top: var(--spacing-xs);
  }
}
