# Agent Zero VS Code Extension - User Guide

## Getting Started

### Installation

1. **From VS Code Marketplace:**
   - Open VS Code
   - Go to Extensions (Ctrl+Shift+X)
   - Search for "Agent Zero"
   - Click Install

2. **From VSIX file:**
   - Download the .vsix file
   - Open VS Code
   - Press Ctrl+Shift+P
   - Type "Extensions: Install from VSIX"
   - Select the downloaded file

### Initial Setup

1. **Configure API Keys:**
   Set up your AI provider API keys in your environment:
   ```bash
   export OPENAI_API_KEY="your-openai-key"
   export ANTHROPIC_API_KEY="your-anthropic-key"
   export GOOGLE_API_KEY="your-google-key"
   ```

2. **Open Settings:**
   - Press Ctrl+, (Cmd+, on Mac)
   - Search for "Agent Zero"
   - Configure your preferred models and settings

3. **Activate Extension:**
   - Press Ctrl+Shift+P
   - Type "Agent Zero: Open Chat"
   - The extension will initialize automatically

## Main Features

### 1. AI Chat Interface

The chat interface is your primary way to interact with Agent Zero.

**Opening the Chat:**
- Press `Ctrl+Shift+A` (Cmd+Shift+A on Mac)
- Or use Command Palette: "Agent Zero: Open Chat"

**Chat Features:**
- **Syntax Highlighting:** Code in responses is automatically highlighted
- **Copy Code:** Click the copy button on code blocks
- **Message History:** Scroll up to see previous conversations
- **Context Awareness:** The AI remembers your conversation context

**Example Conversations:**
```
You: "Help me create a Python function to calculate fibonacci numbers"
Agent: "I'll help you create an efficient fibonacci function..."

You: "Now write unit tests for that function"
Agent: "Here are comprehensive unit tests for the fibonacci function..."
```

### 2. Multi-Agent System

Create specialized agents for different tasks.

**Creating Agents:**
1. Open the Multi-Agent view in the sidebar
2. Click "Create New Agent"
3. Choose from templates:
   - **General Assistant:** For general tasks
   - **Code Developer:** For coding tasks
   - **Code Reviewer:** For code review
   - **Test Engineer:** For testing
   - **Documentation Writer:** For documentation
   - **Research Assistant:** For research
   - **Security Auditor:** For security analysis

**Agent Templates:**

#### Code Developer Agent
- Specializes in writing, debugging, and optimizing code
- Uses lower temperature for more consistent code generation
- Has access to code execution and file editing tools

#### Code Reviewer Agent
- Reviews code for quality, security, and best practices
- Provides detailed feedback and suggestions
- Focuses on identifying potential issues

#### Test Engineer Agent
- Creates comprehensive test suites
- Writes unit tests, integration tests, and end-to-end tests
- Ensures code quality through testing

**Managing Agents:**
- **View Agent Details:** Click on an agent to see its capabilities
- **Assign Tasks:** Right-click an agent to assign specific tasks
- **Create Sub-Agents:** Create specialized child agents
- **Monitor Status:** See which agents are active or working

### 3. Enhanced Memory System

Agent Zero remembers important information across sessions.

**Automatic Memory:**
- Conversations are automatically saved
- Code solutions are remembered
- File contexts are indexed
- Project knowledge is built over time

**Manual Memory Management:**
- **Save Memory:** Use "Agent Zero: Save Memory" command
- **Search Memory:** Use "Agent Zero: Search Knowledge" command
- **Memory Types:**
  - Facts: General information
  - Code: Code snippets and solutions
  - Solutions: Problem-solving approaches
  - Instructions: Step-by-step guides

**Workspace Knowledge:**
The extension automatically analyzes your workspace:
- File structure and organization
- Programming languages used
- Dependencies and configurations
- Code patterns and conventions

### 4. Code Execution

Execute code directly through Agent Zero.

**Supported Languages:**
- Python
- JavaScript/TypeScript
- Java
- C/C++
- Go
- Rust
- Bash/Shell
- PowerShell

**How to Execute Code:**
1. **Through Chat:** Ask the agent to run code
   ```
   You: "Run this Python code: print('Hello, World!')"
   ```

2. **Command Palette:** Use "Agent Zero: Execute Code"

3. **Right-click Menu:** Select code and choose "Execute with Agent Zero"

**Execution Features:**
- **Real-time Output:** See results as they happen
- **Error Handling:** Clear error messages and debugging help
- **Multiple Languages:** Automatic language detection
- **Safe Execution:** Isolated execution environment

### 5. File Operations

Agent Zero can read, write, and manipulate files.

**File Operations:**
- **Read Files:** Agent can read and analyze any file
- **Write Files:** Create new files or modify existing ones
- **Search Files:** Find content across your workspace
- **File Analysis:** Understand code structure and dependencies

**Examples:**
```
You: "Read the main.py file and explain what it does"
Agent: "I'll analyze the main.py file for you..."

You: "Create a new React component called UserProfile"
Agent: "I'll create a UserProfile component with proper TypeScript types..."
```

### 6. Git Integration

Seamless Git operations through Agent Zero.

**Git Features:**
- **Status Checking:** View current repository status
- **Commit Creation:** Create commits with AI-generated messages
- **Branch Management:** Create and switch branches
- **Code Review:** Review changes before committing
- **Conflict Resolution:** Help resolve merge conflicts

**Examples:**
```
You: "Check git status and create a commit for my changes"
Agent: "I'll check your git status and create an appropriate commit..."

You: "Help me resolve this merge conflict"
Agent: "I'll analyze the conflict and suggest a resolution..."
```

### 7. Web Browsing and Research

Agent Zero can browse the web and conduct research.

**Research Capabilities:**
- **Web Search:** Search across multiple search engines
- **Documentation Lookup:** Find official documentation
- **Stack Overflow:** Search for programming solutions
- **GitHub Search:** Find relevant repositories and code examples

**Examples:**
```
You: "Research the latest React 18 features"
Agent: "I'll search for information about React 18 features..."

You: "Find examples of implementing JWT authentication in Node.js"
Agent: "I'll search for JWT authentication examples..."
```

### 8. Terminal Integration

Execute terminal commands through Agent Zero.

**Terminal Features:**
- **Command Execution:** Run any terminal command
- **Multiple Terminals:** Manage multiple terminal sessions
- **Script Execution:** Run shell scripts and batch files
- **Environment Management:** Set environment variables

**Examples:**
```
You: "Install the latest version of Node.js dependencies"
Agent: "I'll run npm install to update your dependencies..."

You: "Run the test suite and show me the results"
Agent: "I'll execute your test suite..."
```

## Advanced Features

### Custom Agent Creation

Create specialized agents for your specific needs.

**Steps to Create Custom Agent:**
1. Click "Create Custom Agent" in the Multi-Agent view
2. Fill in the agent details:
   - **Name:** Give your agent a descriptive name
   - **Description:** Explain what the agent does
   - **System Prompt:** Define the agent's behavior
   - **Capabilities:** Select what the agent can do
   - **Tools:** Choose available tools
   - **Model Configuration:** Select AI model and settings

**Example Custom Agent:**
```
Name: API Documentation Generator
Description: Specializes in creating comprehensive API documentation
System Prompt: You are an expert technical writer specializing in API documentation. Create clear, comprehensive, and developer-friendly documentation.
Capabilities: documentation, analysis, code-review
Tools: file-editor, search-engine, browser
```

### Knowledge Search

Search across all your memories, files, and workspace knowledge.

**Search Interface:**
1. Use "Agent Zero: Search Knowledge" command
2. Enter your search query
3. Filter by:
   - **Type:** Memory, files, or workspace
   - **Language:** Programming language
   - **Time Range:** When the information was created
   - **Tags:** Specific categories

**Search Examples:**
- "React hooks implementation"
- "Authentication middleware code"
- "Database connection setup"
- "Error handling patterns"

### Task Management

Assign and track tasks across multiple agents.

**Task Assignment:**
1. Right-click on an agent
2. Select "Assign Task"
3. Fill in task details:
   - **Title:** Brief task description
   - **Description:** Detailed requirements
   - **Priority:** Low, Medium, High, or Urgent
   - **Dependencies:** Other tasks that must complete first

**Task Monitoring:**
- View task progress in the agent details
- See task completion times
- Track task dependencies
- Monitor agent workload

## Tips and Best Practices

### Effective Communication

**Be Specific:**
```
❌ "Fix my code"
✅ "Fix the authentication error in the login function on line 45"
```

**Provide Context:**
```
❌ "How do I do this?"
✅ "How do I implement JWT authentication in a Node.js Express app?"
```

**Break Down Complex Tasks:**
```
❌ "Build a complete web application"
✅ "Create a React component for user registration, then add form validation"
```

### Memory Management

**Tag Important Information:**
When saving memories, use relevant tags:
- Programming language (python, javascript, etc.)
- Framework (react, express, django, etc.)
- Category (authentication, database, testing, etc.)

**Regular Cleanup:**
Periodically review and organize your memories to keep them relevant.

### Agent Specialization

**Use Specialized Agents:**
- Use Code Developer for implementation
- Use Code Reviewer for quality checks
- Use Test Engineer for testing
- Use Documentation Writer for docs

**Create Project-Specific Agents:**
For large projects, create agents specialized for specific modules or features.

### Workspace Organization

**Keep Projects Organized:**
- Use clear folder structures
- Maintain good documentation
- Keep configuration files updated

**Regular Indexing:**
The extension automatically indexes your workspace, but you can trigger manual re-indexing if needed.

## Troubleshooting

### Common Issues

**Extension Not Activating:**
1. Check VS Code version (requires 1.74+)
2. Restart VS Code
3. Check the Output panel for errors

**API Calls Failing:**
1. Verify API keys are set correctly
2. Check internet connection
3. Verify API key permissions

**Chat Not Responding:**
1. Check if Agent Zero service is running
2. Restart the extension
3. Check the Output panel for errors

**Memory Not Saving:**
1. Check workspace permissions
2. Verify storage quota
3. Restart VS Code

### Getting Help

**Output Panels:**
Check these output channels for debugging:
- Agent Zero
- Agent Zero Service
- Agent Zero API
- Agent Zero Memory

**Log Files:**
Enable debug logging in settings:
```json
{
  "agent-zero.debug.enabled": true,
  "agent-zero.debug.level": "verbose"
}
```

**Support:**
- GitHub Issues: Report bugs and feature requests
- Documentation: Check the latest documentation
- Community: Join discussions and get help

## Keyboard Shortcuts

| Action | Windows/Linux | Mac |
|--------|---------------|-----|
| Open Chat | Ctrl+Shift+A | Cmd+Shift+A |
| New Agent | Ctrl+Shift+N | Cmd+Shift+N |
| Execute Code | Ctrl+Shift+E | Cmd+Shift+E |
| Search Knowledge | Ctrl+Shift+F | Cmd+Shift+F |
| Save Memory | Ctrl+Shift+M | Cmd+Shift+M |

## Settings Reference

### Model Configuration
```json
{
  "agent-zero.models.chat.provider": "openai",
  "agent-zero.models.chat.name": "gpt-4",
  "agent-zero.models.utility.provider": "openai",
  "agent-zero.models.utility.name": "gpt-3.5-turbo",
  "agent-zero.models.embeddings.provider": "openai",
  "agent-zero.models.embeddings.name": "text-embedding-ada-002"
}
```

### Agent Configuration
```json
{
  "agent-zero.agent.prompts": "prompts",
  "agent-zero.agent.memory": "memory",
  "agent-zero.agent.knowledge": "knowledge"
}
```

### Service Configuration
```json
{
  "agent-zero.service.autoStart": true,
  "agent-zero.service.port": 50001,
  "agent-zero.service.host": "localhost"
}
```

### UI Configuration
```json
{
  "agent-zero.ui.theme": "auto",
  "agent-zero.ui.language": "en",
  "agent-zero.ui.fontSize": 14
}
```

## What's Next?

### Upcoming Features
- Enhanced code completion
- Advanced debugging integration
- Team collaboration features
- Plugin system for custom tools
- Mobile companion app

### Learning Resources
- [Developer Guide](./DEVELOPER_GUIDE.md)
- [API Reference](./API_REFERENCE.md)
- [Example Projects](./examples/)
- [Video Tutorials](https://youtube.com/agent-zero)

### Community
- [GitHub Repository](https://github.com/agent-zero-team/agent-zero-vscode)
- [Discord Server](https://discord.gg/agent-zero)
- [Reddit Community](https://reddit.com/r/agentzero)

---

**Happy coding with Agent Zero! 🚀**
