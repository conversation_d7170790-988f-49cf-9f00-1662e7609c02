# Agent Zero VS Code Extension - Developer Guide

## Overview

The Agent Zero VS Code Extension transforms the powerful Agent Zero framework into a native VS Code experience, providing developers with AI-powered assistance directly within their development environment.

## Architecture

### Core Components

#### 1. Extension Host (`extension.ts`)
The main entry point that initializes all managers and providers.

```typescript
export async function activate(context: vscode.ExtensionContext) {
    // Initialize core managers
    configurationManager = new ConfigurationManager();
    modelManager = new ModelManager(configurationManager);
    toolManager = new ToolManager(context);
    enhancedMemoryManager = new EnhancedMemoryManager(context, modelManager);
    multiAgentSystem = new MultiAgentSystem(context, modelManager, toolManager, enhancedMemoryManager);
    
    // Initialize services
    agentZeroService = new AgentZeroService(configurationManager, context);
    apiService = new ApiService(agentZeroService);
    
    // Initialize providers
    agentZeroProvider = new AgentZeroProvider(context, agentManager);
    chatWebviewProvider = new ChatWebviewProvider(context, agentManager);
    multiAgentProvider = new MultiAgentProvider(context, multiAgentSystem);
}
```

#### 2. Core Managers

##### ConfigurationManager
Manages all extension settings and configurations.

```typescript
class ConfigurationManager {
    public get<T>(key: string, defaultValue: T): T
    public getConfiguration(): AgentZeroConfiguration
    public updateConfiguration(updates: Partial<AgentZeroConfiguration>): Promise<void>
}
```

##### ModelManager
Handles AI model interactions across multiple providers.

```typescript
class ModelManager {
    public async processMessage(message: string, context?: any): Promise<string>
    public async generateEmbeddings(text: string): Promise<number[]>
    public switchModel(type: 'chat' | 'utility' | 'embeddings', config: ModelConfig): Promise<void>
}
```

##### ToolManager
Manages and executes various tools and integrations.

```typescript
class ToolManager {
    public async executeTool(toolId: string, parameters: Record<string, any>): Promise<ToolResult>
    public async executeCode(code: string, language: string): Promise<ToolResult>
    public async readFile(filePath: string): Promise<ToolResult>
    public async writeFile(filePath: string, content: string): Promise<ToolResult>
}
```

##### EnhancedMemoryManager
Advanced memory and knowledge management system.

```typescript
class EnhancedMemoryManager {
    public async saveMemory(content: string, type: MemoryType, title?: string): Promise<string>
    public async loadMemory(query: string, type?: MemoryType, limit?: number): Promise<EnhancedMemoryItem[]>
    public async indexCurrentWorkspace(): Promise<void>
}
```

##### MultiAgentSystem
Hierarchical multi-agent management system.

```typescript
class MultiAgentSystem {
    public async createAgentFromTemplate(templateId: string, customName?: string, parentId?: string): Promise<Agent>
    public async createCustomAgent(config: CustomAgentConfig): Promise<Agent>
    public async assignTask(agentId: string, task: AgentTask): Promise<string>
    public async processNextTask(agentId: string): Promise<void>
}
```

#### 3. Services

##### AgentZeroService
Manages the Agent Zero backend service lifecycle.

```typescript
class AgentZeroService {
    public async start(): Promise<boolean>
    public async stop(): Promise<boolean>
    public async restart(): Promise<boolean>
    public getStatus(): ServiceStatus
    public async makeApiRequest(endpoint: string, method: string, data?: any): Promise<any>
}
```

##### ApiService
Provides high-level API for interacting with Agent Zero.

```typescript
class ApiService {
    public async sendChatMessage(message: string, agentId?: string): Promise<string>
    public async executeCode(request: CodeExecutionRequest): Promise<CodeExecutionResult>
    public async performFileOperation(operation: FileOperation): Promise<any>
    public async getAgents(): Promise<AgentInfo[]>
}
```

##### WebSocketService
Real-time communication with Agent Zero backend.

```typescript
class WebSocketService {
    public async connect(): Promise<boolean>
    public send(message: WebSocketMessage): boolean
    public onMessage(type: string, handler: (data: any) => void): void
}
```

#### 4. Enhanced Tools

##### VSCodeFileEditor
Advanced file operations with VS Code integration.

```typescript
class VSCodeFileEditor {
    public async executeOperation(operation: FileEditOperation): Promise<FileEditResult>
    public async listFiles(directoryPath: string, pattern?: string): Promise<FileEditResult>
    public async searchInFiles(searchPath: string, query: string): Promise<FileEditResult>
}
```

##### VSCodeExecutor
Code execution with multiple language support.

```typescript
class VSCodeExecutor {
    public async executeCode(request: ExecutionRequest): Promise<ExecutionResult>
    public async runScript(scriptPath: string, args?: string[]): Promise<ExecutionResult>
    public getRunningExecutions(): number[]
}
```

##### VSCodeBrowser
Web browsing and automation capabilities.

```typescript
class VSCodeBrowser {
    public async executeRequest(request: BrowserRequest): Promise<BrowserResult>
    public async openUrlInExternalBrowser(url: string): Promise<BrowserResult>
}
```

##### VSCodeSearchEngine
Multi-source search capabilities.

```typescript
class VSCodeSearchEngine {
    public async search(request: SearchRequest): Promise<SearchResponse>
    public async searchInWorkspace(query: string, filePattern?: string): Promise<SearchResponse>
    public async showSearchResults(response: SearchResponse): Promise<void>
}
```

##### VSCodeGitIntegration
Git operations with VS Code Git extension integration.

```typescript
class VSCodeGitIntegration {
    public async executeGitOperation(operation: GitOperation): Promise<GitResult>
    public async showGitStatus(): Promise<void>
}
```

##### VSCodeTerminal
Terminal management and command execution.

```typescript
class VSCodeTerminal {
    public async executeCommand(command: TerminalCommand): Promise<TerminalResult>
    public async createTerminal(name?: string): Promise<string>
    public async runScript(scriptPath: string, args?: string[]): Promise<TerminalResult>
}
```

#### 5. Providers

##### AgentZeroProvider
Tree data provider for the main agent view.

##### ChatWebviewProvider
Webview provider for the chat interface.

##### MultiAgentProvider
Tree data provider for the multi-agent system.

## Development Setup

### Prerequisites

- Node.js 16+
- VS Code 1.74+
- TypeScript 4.8+

### Installation

1. Clone the repository:
```bash
git clone https://github.com/agent-zero-team/agent-zero-vscode.git
cd agent-zero-vscode/vscode-extension
```

2. Install dependencies:
```bash
npm install
```

3. Build the extension:
```bash
npm run compile
```

4. Run in development mode:
```bash
npm run watch
```

### Testing

Run the test suite:
```bash
npm test
```

Run integration tests:
```bash
npm run test:integration
```

### Debugging

1. Open the project in VS Code
2. Press F5 to launch the Extension Development Host
3. Set breakpoints in your TypeScript code
4. Test the extension in the new VS Code window

## Configuration

### Extension Settings

The extension supports extensive configuration through VS Code settings:

```json
{
  "agent-zero.models.chat.provider": "openai",
  "agent-zero.models.chat.name": "gpt-4",
  "agent-zero.models.utility.provider": "openai",
  "agent-zero.models.utility.name": "gpt-3.5-turbo",
  "agent-zero.models.embeddings.provider": "openai",
  "agent-zero.models.embeddings.name": "text-embedding-ada-002",
  "agent-zero.agent.prompts": "prompts",
  "agent-zero.agent.memory": "memory",
  "agent-zero.agent.knowledge": "knowledge",
  "agent-zero.service.autoStart": true,
  "agent-zero.service.port": 50001,
  "agent-zero.ui.theme": "auto",
  "agent-zero.ui.language": "en"
}
```

### Environment Variables

Set up your API keys:

```bash
export OPENAI_API_KEY="your-openai-key"
export ANTHROPIC_API_KEY="your-anthropic-key"
export GOOGLE_API_KEY="your-google-key"
export GROQ_API_KEY="your-groq-key"
```

## Extension Points

### Adding New Tools

1. Create a new tool class implementing the tool interface:

```typescript
export class MyCustomTool {
    public async execute(parameters: Record<string, any>): Promise<ToolResult> {
        // Tool implementation
        return {
            success: true,
            output: "Tool executed successfully"
        };
    }
}
```

2. Register the tool in ToolManager:

```typescript
// In ToolManager.initializeTools()
{
    id: 'my-custom-tool',
    name: 'My Custom Tool',
    description: 'Description of what the tool does',
    category: 'Custom',
    enabled: true
}
```

### Adding New Agent Templates

1. Define the agent template:

```typescript
const customTemplate: AgentTemplate = {
    id: 'my-specialist',
    name: 'My Specialist',
    description: 'Specialized agent for specific tasks',
    type: AgentType.CUSTOM,
    systemPrompt: 'You are a specialist in...',
    capabilities: ['specialized-task', 'analysis'],
    tools: ['file-editor', 'search-engine'],
    modelConfig: {
        provider: 'openai',
        model: 'gpt-4',
        temperature: 0.3,
        maxTokens: 4000
    },
    memoryConfig: {
        enabled: true,
        retentionDays: 30,
        importance: 7
    },
    icon: 'star',
    color: '#FF6B6B'
};
```

2. Register in MultiAgentSystem:

```typescript
// In MultiAgentSystem.initializeAgentTemplates()
this.agentTemplates.set(customTemplate.id, customTemplate);
```

### Adding New Model Providers

1. Implement the model provider:

```typescript
private async callCustomProviderAPI(model: any, messages: any[]): Promise<any> {
    const response = await fetch(`${model.baseURL}/chat/completions`, {
        method: 'POST',
        headers: {
            'Authorization': `Bearer ${model.apiKey}`,
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            model: model.name,
            messages: messages,
            max_tokens: model.config.limitOutput || 4000
        })
    });

    const data = await response.json();
    return {
        content: data.choices[0].message.content,
        usage: data.usage
    };
}
```

2. Add to ModelManager.callModelAPI():

```typescript
case 'custom-provider':
    return await this.callCustomProviderAPI(model, messages);
```

## Best Practices

### Error Handling

Always wrap async operations in try-catch blocks:

```typescript
public async someAsyncOperation(): Promise<Result> {
    try {
        const result = await this.performOperation();
        return { success: true, data: result };
    } catch (error) {
        this.outputChannel.appendLine(`Operation failed: ${error}`);
        return { success: false, error: error.toString() };
    }
}
```

### Logging

Use the output channel for debugging:

```typescript
private outputChannel = vscode.window.createOutputChannel('Agent Zero Component');

public logInfo(message: string): void {
    this.outputChannel.appendLine(`[INFO] ${message}`);
}

public logError(message: string, error?: any): void {
    this.outputChannel.appendLine(`[ERROR] ${message}: ${error}`);
}
```

### Resource Management

Always dispose of resources:

```typescript
public dispose(): void {
    this.disposables.forEach(d => d.dispose());
    this.outputChannel.dispose();
    // Clean up other resources
}
```

### Configuration Updates

Listen for configuration changes:

```typescript
vscode.workspace.onDidChangeConfiguration(e => {
    if (e.affectsConfiguration('agent-zero')) {
        this.reloadConfiguration();
    }
});
```

## Testing

### Unit Tests

```typescript
import * as assert from 'assert';
import { ModelManager } from '../core/ModelManager';

suite('ModelManager Tests', () => {
    test('should process message correctly', async () => {
        const modelManager = new ModelManager(mockConfigManager);
        const result = await modelManager.processMessage('Hello');
        assert.ok(result.length > 0);
    });
});
```

### Integration Tests

```typescript
suite('Extension Integration Tests', () => {
    test('should activate extension', async () => {
        const extension = vscode.extensions.getExtension('agent-zero.agent-zero');
        await extension?.activate();
        assert.ok(extension?.isActive);
    });
});
```

## Deployment

### Building for Production

```bash
npm run compile
npm run package
```

### Publishing

```bash
vsce package
vsce publish
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## Troubleshooting

### Common Issues

1. **Extension not activating**: Check the activation events in package.json
2. **Commands not working**: Verify command registration in extension.ts
3. **WebView not loading**: Check CSP settings and resource paths
4. **API calls failing**: Verify API keys and network connectivity

### Debug Mode

Enable debug logging:

```json
{
  "agent-zero.debug.enabled": true,
  "agent-zero.debug.level": "verbose"
}
```

## Resources

- [VS Code Extension API](https://code.visualstudio.com/api)
- [Agent Zero Framework](https://github.com/frdel/agent-zero)
- [TypeScript Documentation](https://www.typescriptlang.org/docs/)
- [WebView API Guide](https://code.visualstudio.com/api/extension-guides/webview)
